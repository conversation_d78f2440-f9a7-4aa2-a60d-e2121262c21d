import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import MobileHeader from './MobileHeader';
import MobileBottomNav from './MobileBottomNav';
import MobileHome from './MobileHome';
import MobileKitchens from './MobileKitchens';
import MobileCabinets from './MobileCabinets';
import MobileAbout from './MobileAbout';
import MobileContact from './MobileContact';

const MobileLayout = () => {
  const [activeTab, setActiveTab] = useState('home');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const getPageTitle = () => {
    switch (activeTab) {
      case 'home': return 'مطابخ خبرة';
      case 'kitchens': return 'المطابخ';
      case 'cabinets': return 'الخزائن';
      case 'about': return 'من نحن';
      case 'contact': return 'تواصل معنا';
      default: return 'مطابخ خبرة';
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'home': return <MobileHome />;
      case 'kitchens': return <MobileKitchens />;
      case 'cabinets': return <MobileCabinets />;
      case 'about': return <MobileAbout />;
      case 'contact': return <MobileContact />;
      default: return <MobileHome />;
    }
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center z-50">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="w-20 h-20 bg-white rounded-2xl flex items-center justify-center mb-4 mx-auto shadow-2xl"
            animate={{ 
              rotate: [0, 360],
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              rotate: { duration: 2, repeat: Infinity, ease: "linear" },
              scale: { duration: 1, repeat: Infinity }
            }}
          >
            <i className="ri-home-4-fill text-3xl text-orange-500"></i>
          </motion.div>
          <motion.h1
            className="text-3xl font-bold text-white mb-2"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            مطابخ خبرة
          </motion.h1>
          <motion.p
            className="text-white/80 text-lg"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            تصميم وتنفيذ المطابخ والخزائن
          </motion.p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <MobileHeader 
        title={getPageTitle()}
        rightAction={
          <motion.button
            className="flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <i className="ri-notification-3-line text-lg"></i>
          </motion.button>
        }
      />

      {/* Content */}
      <main className="pt-16 pb-20 min-h-screen">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="h-full"
          >
            {renderContent()}
          </motion.div>
        </AnimatePresence>
      </main>

      {/* Bottom Navigation */}
      <MobileBottomNav 
        activeTab={activeTab} 
        onTabChange={setActiveTab} 
      />
    </div>
  );
};

export default MobileLayout;
