import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const MobileHome = () => {
  const [heroData, setHeroData] = useState(null);
  const [kitchens, setKitchens] = useState([]);
  const [cabinets, setCabinets] = useState([]);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      // Fetch hero data
      const heroResponse = await fetch('/api/hero');
      const heroResult = await heroResponse.json();
      setHeroData(heroResult);

      // Fetch kitchens (first 4)
      const kitchensResponse = await fetch('/api/kitchens');
      const kitchensResult = await kitchensResponse.json();
      setKitchens(kitchensResult.slice(0, 4));

      // Fetch cabinets (first 4)
      const cabinetsResponse = await fetch('/api/cabinets');
      const cabinetsResult = await cabinetsResponse.json();
      setCabinets(cabinetsResult.slice(0, 4));
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const openWhatsApp = () => {
    const message = "مرحباً، أنا مهتم بخدماتكم في تصميم وتنفيذ المطابخ والخزائن";
    const phoneNumber = "966557611105";
    const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      {heroData && (
        <motion.section
          className="relative h-64 bg-gradient-to-br from-orange-500 to-red-500 overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative z-10 h-full flex flex-col justify-center items-center text-center px-6">
            <motion.h1
              className="text-3xl font-bold text-white mb-3"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {heroData.title}
            </motion.h1>
            <motion.p
              className="text-white/90 text-lg mb-6 leading-relaxed"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              {heroData.subtitle}
            </motion.p>
            <motion.button
              onClick={openWhatsApp}
              className="bg-white text-orange-500 px-8 py-3 rounded-full font-bold text-lg shadow-lg flex items-center gap-2"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <i className="ri-whatsapp-line text-xl"></i>
              تواصل معنا الآن
            </motion.button>
          </div>
        </motion.section>
      )}

      {/* Quick Actions */}
      <motion.section
        className="px-4 py-6"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <div className="grid grid-cols-2 gap-4">
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mb-4">
              <i className="ri-restaurant-line text-2xl text-orange-500"></i>
            </div>
            <h3 className="text-lg font-bold text-gray-800 mb-2">المطابخ</h3>
            <p className="text-gray-600 text-sm">تصاميم عصرية وعملية</p>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-4">
              <i className="ri-archive-line text-2xl text-purple-500"></i>
            </div>
            <h3 className="text-lg font-bold text-gray-800 mb-2">الخزائن</h3>
            <p className="text-gray-600 text-sm">حلول تخزين ذكية</p>
          </motion.div>
        </div>
      </motion.section>

      {/* Featured Kitchens */}
      <motion.section
        className="px-4 py-6"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-800">مطابخ مميزة</h2>
          <motion.button
            className="text-orange-500 font-medium flex items-center gap-1"
            whileHover={{ scale: 1.05 }}
          >
            عرض الكل
            <i className="ri-arrow-left-line"></i>
          </motion.button>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          {kitchens.map((kitchen, index) => (
            <motion.div
              key={kitchen.id}
              className="bg-white rounded-2xl overflow-hidden shadow-lg border border-gray-100"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 * index }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="aspect-square relative overflow-hidden">
                <img
                  src={kitchen.images[0]}
                  alt={kitchen.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded-lg text-xs font-medium">
                  جديد
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-bold text-gray-800 mb-1 text-sm">{kitchen.title}</h3>
                <p className="text-gray-600 text-xs line-clamp-2">{kitchen.description}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Featured Cabinets */}
      <motion.section
        className="px-4 py-6"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.7 }}
      >
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-800">خزائن مميزة</h2>
          <motion.button
            className="text-purple-500 font-medium flex items-center gap-1"
            whileHover={{ scale: 1.05 }}
          >
            عرض الكل
            <i className="ri-arrow-left-line"></i>
          </motion.button>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          {cabinets.map((cabinet, index) => (
            <motion.div
              key={cabinet.id}
              className="bg-white rounded-2xl overflow-hidden shadow-lg border border-gray-100"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 * index }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="aspect-square relative overflow-hidden">
                <img
                  src={cabinet.images[0]}
                  alt={cabinet.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-2 right-2 bg-purple-500 text-white px-2 py-1 rounded-lg text-xs font-medium">
                  مميز
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-bold text-gray-800 mb-1 text-sm">{cabinet.title}</h3>
                <p className="text-gray-600 text-xs line-clamp-2">{cabinet.description}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Contact CTA */}
      <motion.section
        className="px-4 py-6 pb-8"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.9 }}
      >
        <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl p-6 text-center">
          <h3 className="text-xl font-bold text-white mb-2">هل تحتاج استشارة؟</h3>
          <p className="text-white/90 mb-4">تواصل معنا للحصول على استشارة مجانية</p>
          <motion.button
            onClick={openWhatsApp}
            className="bg-white text-orange-500 px-6 py-3 rounded-full font-bold flex items-center gap-2 mx-auto"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <i className="ri-whatsapp-line text-xl"></i>
            تواصل عبر واتساب
          </motion.button>
        </div>
      </motion.section>
    </div>
  );
};

export default MobileHome;
