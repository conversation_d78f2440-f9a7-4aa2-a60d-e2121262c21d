import React from 'react';
import { motion } from 'framer-motion';

const MobileHeader = ({ title, showBack = false, onBack, rightAction }) => {
  return (
    <motion.header 
      className="fixed top-0 left-0 right-0 z-50 bg-white shadow-lg border-b border-gray-100"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center justify-between px-4 py-3 h-16">
        {/* Left Side - Back Button or Logo */}
        <div className="flex items-center">
          {showBack ? (
            <motion.button
              onClick={onBack}
              className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <i className="ri-arrow-right-line text-xl text-gray-700"></i>
            </motion.button>
          ) : (
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                <i className="ri-home-4-line text-white text-lg"></i>
              </div>
              <span className="mr-2 text-lg font-bold text-gray-800">خبرة</span>
            </div>
          )}
        </div>

        {/* Center - Title */}
        <div className="flex-1 text-center">
          <h1 className="text-lg font-bold text-gray-800 truncate">
            {title}
          </h1>
        </div>

        {/* Right Side - Action Button */}
        <div className="flex items-center">
          {rightAction || (
            <motion.button
              className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <i className="ri-more-line text-xl text-gray-700"></i>
            </motion.button>
          )}
        </div>
      </div>
    </motion.header>
  );
};

export default MobileHeader;
