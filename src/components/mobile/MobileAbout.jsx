import React from 'react';
import { motion } from 'framer-motion';

const MobileAbout = () => {
  const stats = [
    { icon: 'ri-calendar-line', value: '5+', label: 'سنوات خبرة' },
    { icon: 'ri-user-smile-line', value: '500+', label: 'عميل راضي' },
    { icon: 'ri-award-line', value: '100%', label: 'جودة مضمونة' },
    { icon: 'ri-tools-line', value: '24/7', label: 'دعم فني' }
  ];

  const services = [
    {
      icon: 'ri-restaurant-line',
      title: 'تصميم المطابخ',
      description: 'تصاميم عصرية وعملية تناسب جميع المساحات',
      color: 'from-orange-500 to-red-500'
    },
    {
      icon: 'ri-archive-line',
      title: 'تصنيع الخزائن',
      description: 'خزائن مخصصة بأعلى معايير الجودة',
      color: 'from-purple-500 to-blue-500'
    },
    {
      icon: 'ri-hammer-line',
      title: 'التركيب والتنفيذ',
      description: 'فريق متخصص لضمان التركيب المثالي',
      color: 'from-green-500 to-teal-500'
    },
    {
      icon: 'ri-customer-service-line',
      title: 'خدمة ما بعد البيع',
      description: 'دعم مستمر وصيانة دورية',
      color: 'from-blue-500 to-indigo-500'
    }
  ];

  const team = [
    {
      name: 'أحمد محمد',
      role: 'مدير التصميم',
      image: '/api/placeholder/150/150',
      experience: '8 سنوات خبرة'
    },
    {
      name: 'سارة أحمد',
      role: 'مهندسة ديكور',
      image: '/api/placeholder/150/150',
      experience: '6 سنوات خبرة'
    },
    {
      name: 'محمد علي',
      role: 'رئيس الفنيين',
      image: '/api/placeholder/150/150',
      experience: '10 سنوات خبرة'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <motion.section
        className="bg-gradient-to-br from-orange-500 to-red-500 p-6 text-center text-white"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          className="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: 'spring' }}
        >
          <i className="ri-home-4-fill text-3xl text-white"></i>
        </motion.div>
        <motion.h1
          className="text-3xl font-bold mb-3"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          عجائب الخبراء
        </motion.h1>
        <motion.p
          className="text-lg opacity-90"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          رائدون في تصميم وتنفيذ المطابخ والخزائن العصرية
        </motion.p>
      </motion.section>

      {/* Stats Section */}
      <motion.section
        className="p-4 -mt-8 relative z-10"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="grid grid-cols-2 gap-4">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.6 + index * 0.1 }}
              >
                <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <i className={`${stat.icon} text-2xl text-orange-500`}></i>
                </div>
                <div className="text-2xl font-bold text-gray-800">{stat.value}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* About Text */}
      <motion.section
        className="p-4"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.7 }}
      >
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <h2 className="text-2xl font-bold text-gray-800 mb-4 text-right">من نحن</h2>
          <p className="text-gray-600 leading-relaxed text-right mb-4">
            شركة عجائب الخبراء هي إحدى الشركات الرائدة في مجال تصميم وتنفيذ المطابخ والخزائن العصرية في المملكة العربية السعودية. نتميز بخبرتنا الواسعة وفريقنا المتخصص في تقديم حلول مبتكرة تلبي احتياجات عملائنا.
          </p>
          <p className="text-gray-600 leading-relaxed text-right">
            نؤمن بأن المطبخ هو قلب المنزل، لذلك نحرص على تقديم تصاميم عملية وجميلة تجمع بين الأناقة والوظائف العملية، مع استخدام أجود الخامات وأحدث التقنيات في التصنيع والتركيب.
          </p>
        </div>
      </motion.section>

      {/* Services */}
      <motion.section
        className="p-4"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.8 }}
      >
        <h2 className="text-2xl font-bold text-gray-800 mb-4 text-right">خدماتنا</h2>
        <div className="space-y-4">
          {services.map((service, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
              initial={{ x: 50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.9 + index * 0.1 }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-start gap-4">
                <div className={`w-12 h-12 bg-gradient-to-r ${service.color} rounded-xl flex items-center justify-center flex-shrink-0`}>
                  <i className={`${service.icon} text-2xl text-white`}></i>
                </div>
                <div className="flex-1 text-right">
                  <h3 className="text-lg font-bold text-gray-800 mb-2">{service.title}</h3>
                  <p className="text-gray-600 text-sm">{service.description}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Team */}
      <motion.section
        className="p-4"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 1.2 }}
      >
        <h2 className="text-2xl font-bold text-gray-800 mb-4 text-right">فريق العمل</h2>
        <div className="space-y-4">
          {team.map((member, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 1.3 + index * 0.1 }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                  {member.name.charAt(0)}
                </div>
                <div className="flex-1 text-right">
                  <h3 className="text-lg font-bold text-gray-800">{member.name}</h3>
                  <p className="text-orange-500 font-medium">{member.role}</p>
                  <p className="text-gray-600 text-sm">{member.experience}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Vision & Mission */}
      <motion.section
        className="p-4 pb-8"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 1.6 }}
      >
        <div className="space-y-4">
          <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl p-6 text-white">
            <div className="flex items-center gap-3 mb-3">
              <i className="ri-eye-line text-2xl"></i>
              <h3 className="text-xl font-bold">رؤيتنا</h3>
            </div>
            <p className="text-white/90 leading-relaxed">
              أن نكون الخيار الأول في المملكة لتصميم وتنفيذ المطابخ والخزائن العصرية، ونساهم في تحسين جودة الحياة في المنازل السعودية.
            </p>
          </div>

          <div className="bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl p-6 text-white">
            <div className="flex items-center gap-3 mb-3">
              <i className="ri-target-line text-2xl"></i>
              <h3 className="text-xl font-bold">رسالتنا</h3>
            </div>
            <p className="text-white/90 leading-relaxed">
              تقديم حلول مبتكرة وعملية في تصميم المطابخ والخزائن، مع الحرص على أعلى معايير الجودة وخدمة العملاء المتميزة.
            </p>
          </div>
        </div>
      </motion.section>
    </div>
  );
};

export default MobileAbout;
