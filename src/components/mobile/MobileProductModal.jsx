import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const MobileProductModal = ({ product, type, onClose }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [socialMedia, setSocialMedia] = useState([]);

  useEffect(() => {
    fetchSocialMedia();
  }, []);

  const fetchSocialMedia = async () => {
    try {
      const response = await fetch('/api/footer');
      const data = await response.json();
      setSocialMedia(data.socialMedia || []);
    } catch (error) {
      console.error('Error fetching social media:', error);
    }
  };

  const openWhatsApp = () => {
    const message = `مرحباً، أنا مهتم بهذا ${type === 'kitchen' ? 'المطبخ' : 'الخزانة'}: ${product.title}`;
    const phoneNumber = "966557611105";
    const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === product.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? product.images.length - 1 : prev - 1
    );
  };

  const getFeatures = () => {
    if (type === 'kitchen') {
      return [
        'تصميم عصري وأنيق',
        'مواد عالية الجودة',
        'ضمان 5 سنوات',
        'تركيب مجاني',
        'خدمة ما بعد البيع',
        'استشارة مجانية'
      ];
    } else {
      return [
        'تصميم عملي ومبتكر',
        'مساحة تخزين مثلى',
        'خامات مقاومة للرطوبة',
        'ضمان 3 سنوات',
        'تركيب مجاني',
        'صيانة دورية'
      ];
    }
  };

  return (
    <motion.div
      className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="absolute inset-x-0 bottom-0 bg-white rounded-t-3xl max-h-[95vh] overflow-hidden"
        initial={{ y: '100%' }}
        animate={{ y: 0 }}
        exit={{ y: '100%' }}
        transition={{ type: 'spring', damping: 25, stiffness: 500 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Handle */}
        <div className="flex justify-center py-3">
          <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between px-4 py-2 border-b border-gray-100">
          <motion.button
            onClick={onClose}
            className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <i className="ri-close-line text-xl text-gray-600"></i>
          </motion.button>
          <h2 className="text-lg font-bold text-gray-800">تفاصيل المنتج</h2>
          <motion.button
            className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <i className="ri-share-line text-xl text-gray-600"></i>
          </motion.button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(95vh-140px)]">
          {/* Image Gallery */}
          <div className="relative h-56 bg-gray-100">
            <img
              src={product.images && product.images[currentImageIndex] ? product.images[currentImageIndex].image_url : '/uploads/placeholder.jpg'}
              alt={product.title}
              className="w-full h-full object-cover"
            />
            
            {/* Navigation Arrows */}
            {product.images.length > 1 && (
              <>
                <motion.button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-black/50 backdrop-blur-sm text-white rounded-full flex items-center justify-center"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <i className="ri-arrow-left-line"></i>
                </motion.button>
                <motion.button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-black/50 backdrop-blur-sm text-white rounded-full flex items-center justify-center"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <i className="ri-arrow-right-line"></i>
                </motion.button>
              </>
            )}

            {/* Image Counter */}
            {product.images && product.images.length > 1 && (
              <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm">
                {currentImageIndex + 1} / {product.images.length}
              </div>
            )}
          </div>

          {/* Thumbnail Images */}
          {product.images && product.images.length > 1 && (
            <div className="p-4 border-b border-gray-100">
              <div className="flex gap-2 overflow-x-auto">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                      currentImageIndex === index
                        ? `border-${type === 'kitchen' ? 'orange' : 'purple'}-500`
                        : 'border-gray-200'
                    }`}
                  >
                    <img
                      src={image.image_url || '/uploads/placeholder.jpg'}
                      alt={`${product.title} - ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Product Info */}
          <div className="p-4">
            <h3 className="text-xl font-bold text-gray-800 mb-2 text-right">
              {product.title}
            </h3>
            <p className="text-gray-600 text-sm leading-relaxed mb-4 text-right">
              {product.description}
            </p>

            {/* Features */}
            <div className="mb-4">
              <div className="flex items-center justify-end mb-3">
                <h4 className="text-lg font-bold text-gray-800 mr-2">المميزات</h4>
                <i className="ri-star-fill text-xl text-yellow-500"></i>
              </div>
              <div className="grid grid-cols-2 gap-2">
                {getFeatures().slice(0, 4).map((feature, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center justify-end text-sm"
                    initial={{ x: 20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <span className="text-gray-700 text-right mr-2 truncate">{feature}</span>
                    <i className="ri-check-line text-lg text-green-500 flex-shrink-0"></i>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Social Media */}
            {socialMedia.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center justify-end mb-3">
                  <h4 className="text-lg font-bold text-gray-800 mr-2">تواصل معنا</h4>
                  <i className="ri-share-line text-xl text-blue-500"></i>
                </div>
                <div className="flex justify-center gap-2 flex-wrap">
                  {socialMedia.map((social, index) => {
                    if (social.platform.toLowerCase().includes('whatsapp')) {
                      return (
                        <motion.button
                          key={index}
                          onClick={openWhatsApp}
                          className="flex items-center justify-center w-10 h-10 bg-green-500 text-white rounded-full shadow-lg"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <i className={social.icon + " text-sm"}></i>
                        </motion.button>
                      );
                    }

                    const getBackgroundClass = (platform) => {
                      const platformLower = platform.toLowerCase();
                      if (platformLower.includes('instagram')) return 'bg-gradient-to-r from-purple-500 to-pink-500';
                      if (platformLower.includes('twitter')) return 'bg-black';
                      if (platformLower.includes('snapchat')) return 'bg-yellow-500';
                      if (platformLower.includes('tiktok')) return 'bg-black';
                      return 'bg-gray-600';
                    };

                    return (
                      <motion.a
                        key={index}
                        href={social.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`flex items-center justify-center w-10 h-10 text-white rounded-full shadow-lg ${getBackgroundClass(social.platform)}`}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <i className={social.icon + " text-sm"}></i>
                      </motion.a>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Bottom Action */}
        <div className="p-4 border-t border-gray-100 bg-white">
          <motion.button
            onClick={openWhatsApp}
            className={`w-full py-4 rounded-2xl font-bold text-lg text-white flex items-center justify-center gap-3 ${
              type === 'kitchen' 
                ? 'bg-gradient-to-r from-orange-500 to-red-500' 
                : 'bg-gradient-to-r from-purple-500 to-blue-500'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <i className="ri-whatsapp-line text-2xl"></i>
            استفسر الآن عبر واتساب
          </motion.button>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default MobileProductModal;
