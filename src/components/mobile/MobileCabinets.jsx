import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import MobileProductModal from './MobileProductModal';

const MobileCabinets = () => {
  const [cabinets, setCabinets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCabinet, setSelectedCabinet] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCabinets, setFilteredCabinets] = useState([]);

  useEffect(() => {
    fetchCabinets();
  }, []);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredCabinets(cabinets);
    } else {
      const filtered = cabinets.filter(cabinet =>
        cabinet.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cabinet.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCabinets(filtered);
    }
  }, [searchQuery, cabinets]);

  const fetchCabinets = async () => {
    try {
      const response = await fetch('/api/cabinets');
      const data = await response.json();
      setCabinets(data);
      setFilteredCabinets(data);
    } catch (error) {
      console.error('Error fetching cabinets:', error);
    } finally {
      setLoading(false);
    }
  };

  const openWhatsApp = (cabinet) => {
    const message = `مرحباً، أنا مهتم بهذه الخزانة: ${cabinet.title}`;
    const phoneNumber = "966557611105";
    const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <motion.div
          className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Search Header */}
      <motion.div
        className="bg-white p-4 shadow-sm border-b border-gray-100"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="relative">
          <input
            type="text"
            placeholder="ابحث عن الخزائن..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-gray-100 rounded-2xl px-4 py-3 pr-12 text-right placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:bg-white transition-all duration-200"
          />
          <i className="ri-search-line absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-xl"></i>
        </div>
      </motion.div>

      {/* Stats */}
      <motion.div
        className="bg-gradient-to-r from-purple-500 to-blue-500 p-4"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
      >
        <div className="flex items-center justify-between text-white">
          <div className="text-center">
            <div className="text-2xl font-bold">{filteredCabinets.length}</div>
            <div className="text-sm opacity-90">خزانة متاحة</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">3+</div>
            <div className="text-sm opacity-90">سنوات ضمان</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">100%</div>
            <div className="text-sm opacity-90">جودة مضمونة</div>
          </div>
        </div>
      </motion.div>

      {/* Cabinets Grid */}
      <div className="p-4">
        {filteredCabinets.length === 0 ? (
          <motion.div
            className="text-center py-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <i className="ri-search-line text-6xl text-gray-300 mb-4"></i>
            <h3 className="text-xl font-bold text-gray-600 mb-2">لا توجد نتائج</h3>
            <p className="text-gray-500">جرب البحث بكلمات مختلفة</p>
          </motion.div>
        ) : (
          <div className="grid grid-cols-1 gap-4">
            {filteredCabinets.map((cabinet, index) => (
              <motion.div
                key={cabinet.id}
                className="bg-white rounded-2xl overflow-hidden shadow-lg border border-gray-100"
                initial={{ y: 50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setSelectedCabinet(cabinet)}
              >
                {/* Image */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={cabinet.images && cabinet.images[0] ? cabinet.images[0].image_url : '/uploads/placeholder.jpg'}
                    alt={cabinet.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-3 right-3 bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                    خزانة عملية
                  </div>
                  <div className="absolute bottom-3 left-3 bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-full text-xs">
                    {cabinet.images ? cabinet.images.length : 0} صورة
                  </div>
                </div>

                {/* Content */}
                <div className="p-4">
                  <h3 className="text-lg font-bold text-gray-800 mb-2 text-right">
                    {cabinet.title}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4 text-right line-clamp-2">
                    {cabinet.description}
                  </p>

                  {/* Features */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    <span className="bg-purple-100 text-purple-600 px-2 py-1 rounded-lg text-xs font-medium">
                      تصميم عملي
                    </span>
                    <span className="bg-green-100 text-green-600 px-2 py-1 rounded-lg text-xs font-medium">
                      مساحة مثلى
                    </span>
                    <span className="bg-blue-100 text-blue-600 px-2 py-1 rounded-lg text-xs font-medium">
                      ضمان 3 سنوات
                    </span>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <motion.button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedCabinet(cabinet);
                      }}
                      className="flex-1 bg-gray-100 text-gray-700 py-3 rounded-xl font-medium flex items-center justify-center gap-2"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <i className="ri-eye-line"></i>
                      عرض التفاصيل
                    </motion.button>
                    <motion.button
                      onClick={(e) => {
                        e.stopPropagation();
                        openWhatsApp(cabinet);
                      }}
                      className="flex-1 bg-green-500 text-white py-3 rounded-xl font-medium flex items-center justify-center gap-2"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <i className="ri-whatsapp-line"></i>
                      استفسار
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Product Modal */}
      <AnimatePresence>
        {selectedCabinet && (
          <MobileProductModal
            product={selectedCabinet}
            type="cabinet"
            onClose={() => setSelectedCabinet(null)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default MobileCabinets;
