import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const MobileContact = () => {
  const [socialMedia, setSocialMedia] = useState([]);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    service: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  useEffect(() => {
    fetchSocialMedia();
  }, []);

  const fetchSocialMedia = async () => {
    try {
      const response = await fetch('/api/footer');
      const data = await response.json();
      setSocialMedia(data.socialMedia || []);
    } catch (error) {
      console.error('Error fetching social media:', error);
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
      setSubmitStatus('success');
      setFormData({
        name: '',
        phone: '',
        email: '',
        service: '',
        message: ''
      });
      
      setTimeout(() => {
        setSubmitStatus(null);
      }, 3000);
    }, 2000);
  };

  const openWhatsApp = () => {
    const message = "مرحباً، أريد الاستفسار عن خدماتكم";
    const phoneNumber = "966557611105";
    const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
  };

  const contactMethods = [
    {
      icon: 'ri-whatsapp-line',
      title: 'واتساب',
      value: '+966 55 761 1105',
      action: openWhatsApp,
      color: 'from-green-500 to-green-600'
    },
    {
      icon: 'ri-phone-line',
      title: 'اتصال مباشر',
      value: '+966 55 761 1105',
      action: () => window.open('tel:+966557611105'),
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: 'ri-mail-line',
      title: 'البريد الإلكتروني',
      value: '<EMAIL>',
      action: () => window.open('mailto:<EMAIL>'),
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: 'ri-map-pin-line',
      title: 'الموقع',
      value: 'الرياض، المملكة العربية السعودية',
      action: () => window.open('https://maps.google.com'),
      color: 'from-red-500 to-red-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <motion.section
        className="bg-gradient-to-br from-green-500 to-teal-500 p-6 text-center text-white"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          className="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: 'spring' }}
        >
          <i className="ri-customer-service-2-fill text-3xl text-white"></i>
        </motion.div>
        <motion.h1
          className="text-3xl font-bold mb-3"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          تواصل معنا
        </motion.h1>
        <motion.p
          className="text-lg opacity-90"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          نحن هنا لخدمتك في أي وقت
        </motion.p>
      </motion.section>

      {/* Quick Contact Methods */}
      <motion.section
        className="p-4 -mt-8 relative z-10"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <div className="space-y-3">
          {contactMethods.map((method, index) => (
            <motion.button
              key={index}
              onClick={method.action}
              className={`w-full bg-gradient-to-r ${method.color} rounded-2xl p-4 text-white shadow-lg`}
              initial={{ x: -50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.6 + index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                  <i className={`${method.icon} text-2xl`}></i>
                </div>
                <div className="flex-1 text-right">
                  <h3 className="font-bold text-lg">{method.title}</h3>
                  <p className="opacity-90 text-sm">{method.value}</p>
                </div>
                <i className="ri-arrow-left-line text-xl opacity-70"></i>
              </div>
            </motion.button>
          ))}
        </div>
      </motion.section>

      {/* Contact Form */}
      <motion.section
        className="p-4"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 1.0 }}
      >
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-right">أرسل رسالة</h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <input
                type="text"
                name="name"
                placeholder="الاسم الكامل"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full bg-gray-100 rounded-xl px-4 py-3 text-right placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:bg-white transition-all duration-200"
              />
            </div>
            
            <div>
              <input
                type="tel"
                name="phone"
                placeholder="رقم الهاتف"
                value={formData.phone}
                onChange={handleInputChange}
                required
                className="w-full bg-gray-100 rounded-xl px-4 py-3 text-right placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:bg-white transition-all duration-200"
              />
            </div>
            
            <div>
              <input
                type="email"
                name="email"
                placeholder="البريد الإلكتروني"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full bg-gray-100 rounded-xl px-4 py-3 text-right placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:bg-white transition-all duration-200"
              />
            </div>
            
            <div>
              <select
                name="service"
                value={formData.service}
                onChange={handleInputChange}
                required
                className="w-full bg-gray-100 rounded-xl px-4 py-3 text-right focus:outline-none focus:ring-2 focus:ring-green-500 focus:bg-white transition-all duration-200"
              >
                <option value="">اختر الخدمة المطلوبة</option>
                <option value="kitchen">تصميم مطبخ</option>
                <option value="cabinet">تصنيع خزانة</option>
                <option value="consultation">استشارة</option>
                <option value="maintenance">صيانة</option>
                <option value="other">أخرى</option>
              </select>
            </div>
            
            <div>
              <textarea
                name="message"
                placeholder="اكتب رسالتك هنا..."
                value={formData.message}
                onChange={handleInputChange}
                rows="4"
                required
                className="w-full bg-gray-100 rounded-xl px-4 py-3 text-right placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:bg-white transition-all duration-200 resize-none"
              ></textarea>
            </div>
            
            <motion.button
              type="submit"
              disabled={isSubmitting}
              className={`w-full py-4 rounded-xl font-bold text-lg text-white flex items-center justify-center gap-3 ${
                isSubmitting 
                  ? 'bg-gray-400 cursor-not-allowed' 
                  : 'bg-gradient-to-r from-green-500 to-teal-500'
              }`}
              whileHover={!isSubmitting ? { scale: 1.02 } : {}}
              whileTap={!isSubmitting ? { scale: 0.98 } : {}}
            >
              {isSubmitting ? (
                <>
                  <motion.div
                    className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  />
                  جاري الإرسال...
                </>
              ) : (
                <>
                  <i className="ri-send-plane-line text-xl"></i>
                  إرسال الرسالة
                </>
              )}
            </motion.button>
          </form>
          
          {submitStatus === 'success' && (
            <motion.div
              className="mt-4 p-4 bg-green-100 border border-green-200 rounded-xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <div className="flex items-center gap-3 text-green-700">
                <i className="ri-check-line text-xl"></i>
                <span className="font-medium">تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.</span>
              </div>
            </motion.div>
          )}
        </div>
      </motion.section>

      {/* Social Media */}
      {socialMedia.length > 0 && (
        <motion.section
          className="p-4"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 1.2 }}
        >
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-xl font-bold text-gray-800 mb-4 text-right">تابعنا على</h3>
            <div className="flex justify-center gap-4 flex-wrap">
              {socialMedia.map((social, index) => {
                const getBackgroundClass = (platform) => {
                  const platformLower = platform.toLowerCase();
                  if (platformLower.includes('whatsapp')) return 'bg-green-500';
                  if (platformLower.includes('instagram')) return 'bg-gradient-to-r from-purple-500 to-pink-500';
                  if (platformLower.includes('twitter')) return 'bg-black';
                  if (platformLower.includes('snapchat')) return 'bg-yellow-500';
                  if (platformLower.includes('tiktok')) return 'bg-black';
                  return 'bg-gray-600';
                };

                return (
                  <motion.a
                    key={index}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`flex items-center justify-center w-14 h-14 text-white rounded-full shadow-lg ${getBackgroundClass(social.platform)}`}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 1.3 + index * 0.1 }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <i className={social.icon + " text-xl"}></i>
                  </motion.a>
                );
              })}
            </div>
          </div>
        </motion.section>
      )}

      {/* Working Hours */}
      <motion.section
        className="p-4 pb-8"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 1.4 }}
      >
        <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-2xl p-6 text-white">
          <h3 className="text-xl font-bold mb-4 text-right">ساعات العمل</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="opacity-90">9:00 ص - 6:00 م</span>
              <span>السبت - الخميس</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="opacity-90">مغلق</span>
              <span>الجمعة</span>
            </div>
          </div>
        </div>
      </motion.section>
    </div>
  );
};

export default MobileContact;
