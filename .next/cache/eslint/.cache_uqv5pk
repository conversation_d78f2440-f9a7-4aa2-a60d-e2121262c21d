[{"/var/www/html/pages/_app.js": "1", "/var/www/html/pages/_document.js": "2", "/var/www/html/pages/admin.js": "3", "/var/www/html/pages/api/[...slug].js": "4", "/var/www/html/pages/api/cabinets.js": "5", "/var/www/html/pages/api/footer.js": "6", "/var/www/html/pages/api/hero.js": "7", "/var/www/html/pages/api/kitchens.js": "8", "/var/www/html/pages/api/why-choose-us.js": "9", "/var/www/html/pages/cabinets.js": "10", "/var/www/html/pages/index.js": "11", "/var/www/html/pages/kitchens.js": "12", "/var/www/html/src/admin/components/Dashboard.jsx": "13", "/var/www/html/src/admin/components/Header.jsx": "14", "/var/www/html/src/admin/components/Login.jsx": "15", "/var/www/html/src/admin/components/Sidebar.jsx": "16", "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx": "17", "/var/www/html/src/admin/components/sections/DashboardHome.jsx": "18", "/var/www/html/src/admin/components/sections/FooterManagement.jsx": "19", "/var/www/html/src/admin/components/sections/HeroManagement.jsx": "20", "/var/www/html/src/admin/components/sections/KitchensManagement.jsx": "21", "/var/www/html/src/admin/components/sections/UserManagement.jsx": "22", "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx": "23", "/var/www/html/src/admin/context/AuthContext.jsx": "24", "/var/www/html/src/admin/context/DataContext.jsx": "25", "/var/www/html/src/admin/utils/initDatabase.js": "26", "/var/www/html/src/components/CabinetGallery.jsx": "27", "/var/www/html/src/components/CallToAction.jsx": "28", "/var/www/html/src/components/Footer.jsx": "29", "/var/www/html/src/components/HeroSection.jsx": "30", "/var/www/html/src/components/KitchenGallery.jsx": "31", "/var/www/html/src/components/Navbar.jsx": "32", "/var/www/html/src/components/SEO.jsx": "33", "/var/www/html/src/components/Testimonials.jsx": "34", "/var/www/html/src/components/WhyChooseUs.jsx": "35", "/var/www/html/src/config/api.js": "36", "/var/www/html/src/config/env.js": "37", "/var/www/html/src/components/mobile/MobileAbout.jsx": "38", "/var/www/html/src/components/mobile/MobileBottomNav.jsx": "39", "/var/www/html/src/components/mobile/MobileCabinets.jsx": "40", "/var/www/html/src/components/mobile/MobileContact.jsx": "41", "/var/www/html/src/components/mobile/MobileHeader.jsx": "42", "/var/www/html/src/components/mobile/MobileHome.jsx": "43", "/var/www/html/src/components/mobile/MobileKitchens.jsx": "44", "/var/www/html/src/components/mobile/MobileLayout.jsx": "45", "/var/www/html/src/components/mobile/MobileProductModal.jsx": "46"}, {"size": 463, "mtime": 1752635557499, "results": "47", "hashOfConfig": "48"}, {"size": 1725, "mtime": 1752631575132, "results": "49", "hashOfConfig": "48"}, {"size": 3441, "mtime": 1752631724686, "results": "50", "hashOfConfig": "48"}, {"size": 1269, "mtime": 1752634460814, "results": "51", "hashOfConfig": "48"}, {"size": 477, "mtime": 1752634978610, "results": "52", "hashOfConfig": "48"}, {"size": 475, "mtime": 1752634943717, "results": "53", "hashOfConfig": "48"}, {"size": 645, "mtime": 1752634586689, "results": "54", "hashOfConfig": "48"}, {"size": 477, "mtime": 1752634960134, "results": "55", "hashOfConfig": "48"}, {"size": 482, "mtime": 1752634995310, "results": "56", "hashOfConfig": "48"}, {"size": 16812, "mtime": 1752645642196, "results": "57", "hashOfConfig": "48"}, {"size": 4578, "mtime": 1752704300372, "results": "58", "hashOfConfig": "48"}, {"size": 17055, "mtime": 1752645598689, "results": "59", "hashOfConfig": "48"}, {"size": 3566, "mtime": 1751444435516, "results": "60", "hashOfConfig": "48"}, {"size": 5952, "mtime": 1751443429426, "results": "61", "hashOfConfig": "48"}, {"size": 9709, "mtime": 1751561588128, "results": "62", "hashOfConfig": "48"}, {"size": 10324, "mtime": 1751443430306, "results": "63", "hashOfConfig": "48"}, {"size": 18409, "mtime": 1751686925506, "results": "64", "hashOfConfig": "48"}, {"size": 9439, "mtime": 1751596455083, "results": "65", "hashOfConfig": "48"}, {"size": 17788, "mtime": 1751601289625, "results": "66", "hashOfConfig": "48"}, {"size": 11914, "mtime": 1751667340164, "results": "67", "hashOfConfig": "48"}, {"size": 17940, "mtime": 1751686839673, "results": "68", "hashOfConfig": "48"}, {"size": 14750, "mtime": 1751443432969, "results": "69", "hashOfConfig": "48"}, {"size": 12975, "mtime": 1751601688079, "results": "70", "hashOfConfig": "48"}, {"size": 3828, "mtime": 1751478236235, "results": "71", "hashOfConfig": "48"}, {"size": 10376, "mtime": 1751606566385, "results": "72", "hashOfConfig": "48"}, {"size": 3715, "mtime": 1751682164034, "results": "73", "hashOfConfig": "48"}, {"size": 38585, "mtime": 1752646305477, "results": "74", "hashOfConfig": "48"}, {"size": 923, "mtime": 1751443427609, "results": "75", "hashOfConfig": "48"}, {"size": 6319, "mtime": 1752645029055, "results": "76", "hashOfConfig": "48"}, {"size": 4282, "mtime": 1752643007566, "results": "77", "hashOfConfig": "48"}, {"size": 41315, "mtime": 1752646178919, "results": "78", "hashOfConfig": "48"}, {"size": 16864, "mtime": 1752631886012, "results": "79", "hashOfConfig": "48"}, {"size": 5001, "mtime": 1752632019825, "results": "80", "hashOfConfig": "48"}, {"size": 3517, "mtime": 1751443429007, "results": "81", "hashOfConfig": "48"}, {"size": 9400, "mtime": 1751696106894, "results": "82", "hashOfConfig": "48"}, {"size": 961, "mtime": 1752643644620, "results": "83", "hashOfConfig": "48"}, {"size": 5079, "mtime": 1752632642474, "results": "84", "hashOfConfig": "48"}, {"size": 9800, "mtime": 1752703957255, "results": "85", "hashOfConfig": "48"}, {"size": 3314, "mtime": 1752646795554, "results": "86", "hashOfConfig": "48"}, {"size": 8389, "mtime": 1752704113755, "results": "87", "hashOfConfig": "48"}, {"size": 13109, "mtime": 1752647177676, "results": "88", "hashOfConfig": "48"}, {"size": 2067, "mtime": 1752704615289, "results": "89", "hashOfConfig": "48"}, {"size": 12259, "mtime": 1752704821738, "results": "90", "hashOfConfig": "48"}, {"size": 8378, "mtime": 1752704071433, "results": "91", "hashOfConfig": "48"}, {"size": 4098, "mtime": 1752703890147, "results": "92", "hashOfConfig": "48"}, {"size": 10892, "mtime": 1752704935350, "results": "93", "hashOfConfig": "48"}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lomtko", {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/var/www/html/pages/_app.js", [], [], "/var/www/html/pages/_document.js", [], [], "/var/www/html/pages/admin.js", [], [], "/var/www/html/pages/api/[...slug].js", [], [], "/var/www/html/pages/api/cabinets.js", [], [], "/var/www/html/pages/api/footer.js", [], [], "/var/www/html/pages/api/hero.js", [], [], "/var/www/html/pages/api/kitchens.js", [], [], "/var/www/html/pages/api/why-choose-us.js", [], [], "/var/www/html/pages/cabinets.js", ["232", "233", "234", "235"], [], "/var/www/html/pages/index.js", [], [], "/var/www/html/pages/kitchens.js", ["236", "237", "238", "239"], [], "/var/www/html/src/admin/components/Dashboard.jsx", [], [], "/var/www/html/src/admin/components/Header.jsx", [], [], "/var/www/html/src/admin/components/Login.jsx", [], [], "/var/www/html/src/admin/components/Sidebar.jsx", [], [], "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx", ["240", "241"], [], "/var/www/html/src/admin/components/sections/DashboardHome.jsx", [], [], "/var/www/html/src/admin/components/sections/FooterManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/HeroManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/KitchensManagement.jsx", ["242", "243"], [], "/var/www/html/src/admin/components/sections/UserManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx", [], [], "/var/www/html/src/admin/context/AuthContext.jsx", [], [], "/var/www/html/src/admin/context/DataContext.jsx", [], [], "/var/www/html/src/admin/utils/initDatabase.js", ["244"], [], "/var/www/html/src/components/CabinetGallery.jsx", ["245", "246", "247", "248", "249", "250"], [], "/var/www/html/src/components/CallToAction.jsx", [], [], "/var/www/html/src/components/Footer.jsx", ["251"], [], "/var/www/html/src/components/HeroSection.jsx", ["252"], [], "/var/www/html/src/components/KitchenGallery.jsx", ["253", "254", "255", "256", "257", "258"], [], "/var/www/html/src/components/Navbar.jsx", [], [], "/var/www/html/src/components/SEO.jsx", [], [], "/var/www/html/src/components/Testimonials.jsx", [], [], "/var/www/html/src/components/WhyChooseUs.jsx", ["259"], [], "/var/www/html/src/config/api.js", [], [], "/var/www/html/src/config/env.js", [], [], "/var/www/html/src/components/mobile/MobileAbout.jsx", [], [], "/var/www/html/src/components/mobile/MobileBottomNav.jsx", [], [], "/var/www/html/src/components/mobile/MobileCabinets.jsx", ["260"], [], "/var/www/html/src/components/mobile/MobileContact.jsx", [], [], "/var/www/html/src/components/mobile/MobileHeader.jsx", [], [], "/var/www/html/src/components/mobile/MobileHome.jsx", ["261", "262"], [], "/var/www/html/src/components/mobile/MobileKitchens.jsx", ["263"], [], "/var/www/html/src/components/mobile/MobileLayout.jsx", [], [], "/var/www/html/src/components/mobile/MobileProductModal.jsx", ["264", "265"], [], {"ruleId": "266", "severity": 1, "message": "267", "line": 75, "column": 6, "nodeType": "268", "endLine": 75, "endColumn": 8, "suggestions": "269"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 127, "column": 21, "nodeType": "272", "endLine": 131, "endColumn": 23}, {"ruleId": "270", "severity": 1, "message": "271", "line": 178, "column": 29, "nodeType": "272", "endLine": 182, "endColumn": 31}, {"ruleId": "270", "severity": 1, "message": "271", "line": 199, "column": 31, "nodeType": "272", "endLine": 203, "endColumn": 33}, {"ruleId": "266", "severity": 1, "message": "273", "line": 78, "column": 6, "nodeType": "268", "endLine": 78, "endColumn": 8, "suggestions": "274"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 130, "column": 21, "nodeType": "272", "endLine": 134, "endColumn": 23}, {"ruleId": "270", "severity": 1, "message": "271", "line": 181, "column": 29, "nodeType": "272", "endLine": 185, "endColumn": 31}, {"ruleId": "270", "severity": 1, "message": "271", "line": 202, "column": 31, "nodeType": "272", "endLine": 206, "endColumn": 33}, {"ruleId": "270", "severity": 1, "message": "271", "line": 285, "column": 15, "nodeType": "272", "endLine": 289, "endColumn": 17}, {"ruleId": "270", "severity": 1, "message": "271", "line": 318, "column": 23, "nodeType": "272", "endLine": 318, "endColumn": 101}, {"ruleId": "270", "severity": 1, "message": "271", "line": 277, "column": 15, "nodeType": "272", "endLine": 281, "endColumn": 17}, {"ruleId": "270", "severity": 1, "message": "271", "line": 310, "column": 23, "nodeType": "272", "endLine": 310, "endColumn": 101}, {"ruleId": "275", "severity": 1, "message": "276", "line": 138, "column": 1, "nodeType": "277", "endLine": 144, "endColumn": 2}, {"ruleId": "266", "severity": 1, "message": "278", "line": 104, "column": 6, "nodeType": "268", "endLine": 104, "endColumn": 8, "suggestions": "279"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 287, "column": 23, "nodeType": "272", "endLine": 291, "endColumn": 25}, {"ruleId": "270", "severity": 1, "message": "271", "line": 401, "column": 25, "nodeType": "272", "endLine": 405, "endColumn": 27}, {"ruleId": "270", "severity": 1, "message": "271", "line": 531, "column": 29, "nodeType": "272", "endLine": 535, "endColumn": 31}, {"ruleId": "270", "severity": 1, "message": "271", "line": 685, "column": 27, "nodeType": "272", "endLine": 689, "endColumn": 29}, {"ruleId": "270", "severity": 1, "message": "271", "line": 700, "column": 19, "nodeType": "272", "endLine": 704, "endColumn": 21}, {"ruleId": "266", "severity": 1, "message": "280", "line": 58, "column": 6, "nodeType": "268", "endLine": 58, "endColumn": 8, "suggestions": "281"}, {"ruleId": "266", "severity": 1, "message": "282", "line": 47, "column": 6, "nodeType": "268", "endLine": 47, "endColumn": 8, "suggestions": "283"}, {"ruleId": "266", "severity": 1, "message": "284", "line": 106, "column": 6, "nodeType": "268", "endLine": 106, "endColumn": 8, "suggestions": "285"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 408, "column": 19, "nodeType": "272", "endLine": 412, "endColumn": 21}, {"ruleId": "270", "severity": 1, "message": "271", "line": 480, "column": 25, "nodeType": "272", "endLine": 484, "endColumn": 27}, {"ruleId": "270", "severity": 1, "message": "271", "line": 610, "column": 29, "nodeType": "272", "endLine": 614, "endColumn": 31}, {"ruleId": "270", "severity": 1, "message": "271", "line": 764, "column": 27, "nodeType": "272", "endLine": 768, "endColumn": 29}, {"ruleId": "270", "severity": 1, "message": "271", "line": 779, "column": 19, "nodeType": "272", "endLine": 783, "endColumn": 21}, {"ruleId": "266", "severity": 1, "message": "286", "line": 99, "column": 6, "nodeType": "268", "endLine": 99, "endColumn": 8, "suggestions": "287"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 131, "column": 19, "nodeType": "272", "endLine": 135, "endColumn": 21}, {"ruleId": "270", "severity": 1, "message": "271", "line": 194, "column": 17, "nodeType": "272", "endLine": 198, "endColumn": 19}, {"ruleId": "270", "severity": 1, "message": "271", "line": 246, "column": 17, "nodeType": "272", "endLine": 250, "endColumn": 19}, {"ruleId": "270", "severity": 1, "message": "271", "line": 131, "column": 19, "nodeType": "272", "endLine": 135, "endColumn": 21}, {"ruleId": "270", "severity": 1, "message": "271", "line": 108, "column": 13, "nodeType": "272", "endLine": 112, "endColumn": 15}, {"ruleId": "270", "severity": 1, "message": "271", "line": 158, "column": 21, "nodeType": "272", "endLine": 162, "endColumn": 23}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fallbackCabinets'. Either include it or remove the dependency array.", "ArrayExpression", ["288"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fallbackKitchens'. Either include it or remove the dependency array.", ["289"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'defaultCabinets'. Either include it or remove the dependency array.", ["290"], "React Hook useEffect has a missing dependency: 'defaultFooterData'. Either include it or remove the dependency array.", ["291"], "React Hook useEffect has a missing dependency: 'defaultHeroData'. Either include it or remove the dependency array.", ["292"], "React Hook useEffect has a missing dependency: 'defaultKitchens'. Either include it or remove the dependency array.", ["293"], "React Hook useEffect has a missing dependency: 'defaultWhyChooseUsData'. Either include it or remove the dependency array.", ["294"], {"desc": "295", "fix": "296"}, {"desc": "297", "fix": "298"}, {"desc": "299", "fix": "300"}, {"desc": "301", "fix": "302"}, {"desc": "303", "fix": "304"}, {"desc": "305", "fix": "306"}, {"desc": "307", "fix": "308"}, "Update the dependencies array to be: [fallbackCabinets]", {"range": "309", "text": "310"}, "Update the dependencies array to be: [fallback<PERSON><PERSON><PERSON>]", {"range": "311", "text": "312"}, "Update the dependencies array to be: [defaultCabinets]", {"range": "313", "text": "314"}, "Update the dependencies array to be: [defaultFooterData]", {"range": "315", "text": "316"}, "Update the dependencies array to be: [defaultHeroData]", {"range": "317", "text": "318"}, "Update the dependencies array to be: [default<PERSON><PERSON>ens]", {"range": "319", "text": "320"}, "Update the dependencies array to be: [defaultWhyChooseUsData]", {"range": "321", "text": "322"}, [2924, 2926], "[fallbackCabinets]", [3099, 3101], "[fallback<PERSON><PERSON><PERSON>]", [3820, 3822], "[defaultCabinets]", [2493, 2495], "[defaultFooterData]", [1825, 1827], "[defaultHeroData]", [3944, 3946], "[default<PERSON><PERSON><PERSON>]", [3175, 3177], "[defaultWhyChooseUsData]"]